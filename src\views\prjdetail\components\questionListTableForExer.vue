<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue';
import type { questionItem } from '@/utils/type';
import {
  ansType,
  qMode,
  qModeDict,
  qWeight,
  qTypeDict,
  qWeightDict,
  ansModeDict,
  answerTypeDict
} from '@/utils/constant';
import { ElMessage, ElMessageBox } from 'element-plus';
import MyFlipper from '@/components/MyFlipper.vue';
import FormSwitch from '@/components/FormSwitch.vue';
import type { params2GetQuesList } from '@/apis/path/prjdetail';
import { findKeyByValue, transToIcon } from '@/utils/func';
import {
  deleteQuestionApi,
  params2DelQues,
  getQuesListApi,
  deleteMultipleQuestionApi
} from '@/apis/path/prjdetail';
import { emitter } from '@/utils/emitter';
import { Event } from '@/types/event';
import { renderMarkdown } from '@/utils/markdown';
import { processAllLatexEquations } from '@/utils/latexUtils';
import { useDialogStore } from '@/stores/dialog';
import router from '@/router';

const emits = defineEmits(['openDrawer', 'refresh']);
const props = defineProps({
  exerciseId: {
    type: String,
    required: false
  },
  refreshFlag: {
    type: Boolean,
    required: false
  }
});

const dialogStore = useDialogStore();

const currentPage = ref(1); // 当前页数
const pageSize = ref(10); // 页大小
const total = ref(0); // 问题总条数

const questionList = ref<questionItem[]>([]); // 要展示的问题列表数据
const questionListRef = ref(); // list表格
const params4getQList = ref<params2GetQuesList>({
  uniqueCode: props.exerciseId,
  limit: pageSize.value,
  current: currentPage.value,
  questionStatus: ansType.default,
  questionType: '',
  questionNecessity: qMode.default,
  questionWeight: qWeight.default,
  keyword: ''
});

// 获取questionList
const getQuestionList = () => {
  getQuesListApi(params4getQList.value).then((res) => {
    if (res.success) {
      questionList.value = res.data.records;
      total.value = res.data.total;
    }
  });
};

const handleQuestionDetail = (row: questionItem) => {
  emitter.emit(Event.SHOW_ANSWER_DRAWER, { mode: 3, item: row.questionId });
};

// 处理换页
const handlePageChange = (newPage: number) => {
  currentPage.value = newPage;
  params4getQList.value.current = newPage;
  getQuestionList();
};

// 计算列表index
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize.value + index + 1;
};
// 处理回答问题
const handleAnswerQues = (row: questionItem) => {
  //emitter.emit(Event.SHOW_ANSWER_DRAWER, { mode: 2, item: row });
  dialogStore.question = row;
  dialogStore.answerDialogVisible = true;
};

// 处理编辑问题
const handleEditQues = (row: questionItem) => {
  emitter.emit(Event.SHOW_QUESTION_DRAWER, { mode: 1, item: row });
};

const goAnswer = (row: questionItem) => {
  router.push(
    `/answer?questionId=${row.questionId}&uniqueCode=${params4getQList.value.uniqueCode}&type=2`
  );
};

// 处理删除问题
const handleDeleteQues = (row: questionItem) => {
  ElMessageBox.confirm('确定删除问题吗？', '删除问题', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const params = ref<params2DelQues>({
        questionId: row.questionId
      });
      deleteQuestionApi(params.value).then((res) => {
        if (res.success) {
          getQuestionList();
          emitter.emit(Event.REMOVE_QUESTION, row.questionId);
          ElMessage.success('删除成功');
        } else {
          ElMessage.error(res.message);
        }
      });
    })
    .catch();
};

// 处理搜索=>formSwitch
const handleSearch = (key: string) => {
  params4getQList.value.keyword = key;
  getQuestionList();
};
// 处理设置当前状态=>formSwitch
const handleSetCur = (cur: any) => {
  let quesType = findKeyByValue(cur.curQType, qTypeDict) as string;
  params4getQList.value.questionType = quesType === '全部问题类型' ? '' : quesType;
  params4getQList.value.questionNecessity = cur.curQMode;
  params4getQList.value.questionWeight = cur.curQWeight;
  params4getQList.value.questionStatus = cur.curAnsType;
  getQuestionList();
};

const deleteMultiple = () => {
  const selection = questionListRef.value?.getSelectionRows() || [];

  if (selection.length === 0) {
    ElMessage.warning('请选择要删除的问题');
    return;
  }

  // 收集选中行的 ID
  const idsToDelete = selection.map((row) => row.questionId);

  // 显示确认对话框
  ElMessageBox.confirm('确定要删除选中的问题吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      // 调用删除接口
      const res: any = await deleteMultipleQuestionApi(idsToDelete);
      if (res.success) {
        getQuestionList();
        ElMessage.success('删除成功');
      }
    })
    .catch(() => {});
};

onMounted(() => {
  getQuestionList();
  emitter.on(Event.REFRESH_QUESTION_LIST, getQuestionList);
});
onUnmounted(() => {
  emitter.off(Event.REFRESH_QUESTION_LIST, getQuestionList);
});
</script>

<template>
  <div class="question-list-wrapper">
    <div class="head-wrapper">
      <form-switch
        :need-left-text="true"
        :need-ans-type="true"
        :need-ques-weight="true"
        :need-ques-ness="true"
        :need-ques-type="true"
        :need-search="true"
        @setcur="handleSetCur"
        @search="handleSearch"
        placeholder="请输入关键字"
      >
      </form-switch>
    </div>
    <div class="content-wrapper">
      <CmpButton type="primary" class="w130" @click="deleteMultiple">批量删除</CmpButton>
      <div class="question-content">
        <el-table
          ref="questionListRef"
          :data="questionList"
          style="width: 100%; height: 100%"
          empty-text="暂无数据"
          :row-style="{ height: '46px', overflow: 'visible' }"
          :cell-style="{ height: '46px', width: '100%', overflow: 'visible' }"
          :header-cell-style="{
            height: '46px',
            'font-weight': 600,
            'font-size': '14px',
            color: '#333333'
          }"
        >
          <el-table-column
            type="selection"
            width="50"
            :selectable="(row) => row.canDelete"
          ></el-table-column>
          <el-table-column type="index" :index="indexMethod" label="序号" width="60">
          </el-table-column>
          <el-table-column prop="keyword" label="关键字" width="190" header-align="center">
            <template #default="{ row }">
              <div>
                <el-tooltip placement="top" :show-after="200" popper-class="tooltip-width">
                  <template #content>
                    <div class="tooltip-content" v-html="row.keyword"></div>
                  </template>
                  <div class="ellipsis-text" v-html="row.keyword"></div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="questionType" label="问题类型" width="110" align="center">
          </el-table-column>
          <el-table-column prop="questionNecessity" label="问题属性" width="100" align="center">
            <template #default="{ row }">
              {{ findKeyByValue(row.questionNecessity, qModeDict) as string }}
            </template>
          </el-table-column>
          <el-table-column prop="questionWeight" label="公开性" width="100" align="center">
            <template #default="{ row }">
              {{ findKeyByValue(row.questionWeight, qWeightDict) as string }}
            </template>
          </el-table-column>
          <el-table-column label="提问人" width="100" align="center">
            <template #default="{ row }">
              <div class="ellipsis-cell">
                <el-tooltip placement="top" :show-after="200" popper-class="tooltip-width">
                  <template #content>
                    <div class="tooltip-content">{{ row.userName }}</div>
                  </template>
                  <div class="ellipsis-text">{{ row.userName }}</div>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="提问时间" width="180" align="center">
          </el-table-column>
          <el-table-column prop="answerNumber" label="答案数" width="70" align="center">
          </el-table-column>
          <el-table-column label="操作" width="300" align="center">
            <template #default="{ row }">
              <span class="operationBlock">
                <span @click="handleQuestionDetail(row)" class="operationBtn">预览</span>
                <span @click="handleEditQues(row)" class="operationBtn">编辑</span>
                <span @click="handleAnswerQues(row)" class="operationBtn">回答</span>
                <span @click="goAnswer(row)" class="operationBtn">答案</span>
                <span
                  @click="handleDeleteQues(row)"
                  :class="row.canDelete ? 'operationBtn' : 'disabledBtn'"
                  >删除</span
                >
              </span>
            </template>
          </el-table-column>
        </el-table>
        <my-flipper
          @change-page="handlePageChange"
          :current="currentPage"
          :page-size="pageSize"
          :total="total"
        ></my-flipper>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 所有单元格的基本样式 */
:deep(.el-table .el-table__cell) {
  height: 65px;
  overflow: hidden;
  width: 100%;
  /* 强制宽度为100% */
}

:deep(.el-table .el-table__cell div) {
  max-height: 65px;
  overflow: hidden;
  width: 100% !important;
  /* 强制宽度为100% */
}

.ellipsis-text {
  display: block;
  white-space: nowrap;
  /* 不换行 */
  overflow: hidden;
  /* 隐藏溢出内容 */
  text-overflow: ellipsis;
  /* 显示省略号 */
  max-width: 100%;
  /* 最大宽度 */
  width: 100%;
  /* 占满容器 */
  color: black;
  /* 确保文字为黑色 */
  position: relative;
  /* 为渐变遮罩定位 */
}

/* 确保渲染后内部的文本也有省略号效果 */
:deep(.ellipsis-text > p) {
  white-space: nowrap;
  /* 不换行 */
  overflow: hidden;
  /* 隐藏溢出内容 */
  text-overflow: ellipsis;
  /* 显示省略号 */
  display: inline-block;
  max-width: 100%;
}

/* 为公式添加渐变遮罩效果 */
.ellipsis-text::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 15px;
  /* 渐变区域宽度 */
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0), white);
  /* 从透明到白色的渐变 */
  pointer-events: none;
  /* 确保不影响鼠标事件 */
}

/* Tooltip 内容样式 */
.tooltip-content {
  max-width: 400px;
  max-height: 300px;
  overflow: auto;
  padding: 10px;
  border-radius: 4px;
}

.question-list-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;

  .head-wrapper {
    display: block;
    width: 100%;
  }

  .content-wrapper {
    .w130 {
      width: 120px;
      height: 35px;
      font-size: 14px;
      border-radius: 4px;
      margin-left: 15px;
    }

    .question-content {
      margin-top: 10px;
      border-top: 1px solid var(--color-boxborder);
      .operationBlock {
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        flex-direction: row;
        color: var(--color-primary);

        .operationBtn {
          padding: 0 10px;

          &:hover {
            cursor: pointer;
            color: var(--color-primary);
            font-weight: 600;
            /* text-decoration: underline; */
          }
        }

        .disabledBtn {
          padding: 0 10px;
          color: var(--color-light);

          &:hover {
            cursor: no-drop;
          }
        }
      }
    }
  }

  &.invalid {
    color: var(--color-invalid);
    border: 1px solid var(--color-boxborder);
    background-color: var(--color-light);

    &:hover {
      font-weight: 400;
      cursor: not-allowed;
    }
  }

  &.lackAnswer {
    color: black;
    background-color: var(--color-light);
    border: 1px solid var(--color-boxborder);
  }
}

/* 关联文本和关键词内容容器 */
.explantion {
  width: 100%;
  justify-content: flex-start;
  white-space: nowrap;
  overflow-x: auto;
  /* 允许水平滚动 */
  overflow-y: hidden;
  /* 禁止垂直滚动 */
  display: block;
}

/* 内容中的段落 */
:deep(.explantion p) {
  white-space: nowrap;
  margin: 0;
  /* 移除段落边距 */
  padding: 0;
  /* 移除段落内边距 */
  display: inline-block;
}

::-webkit-scrollbar {
  height: 5px;
  /* 滚动条高度 */
}
</style>
