<script setup lang="ts">
import { ref, watch } from "vue"
import type { lectureType } from "@/utils/type"
import { ElMessage } from "element-plus"
import { storeToRefs } from "pinia";
import { useDrawerControllerStore } from "@/stores/drawerController";
import { processAllLatexEquations } from '@/utils/latexUtils';

// import {
//   type params4updateVideoLecture,
//   updateVideoLecture,
// } from "@/apis/path/createProject"
const model = defineModel()
const props = defineProps({
  step: {
    type: Number,
    required: true,
  },
  index: {
    // 如果是第一条，index0，不能取消选中段首
    type: Number,
    required: true,
  },
  lecture: {
    type: Object,
    required: true,
  },
})
const curLecture = ref<lectureType[]>([])
const stepMode = ref<number>()
const lectureIndex = ref<number>()
const isEditing = ref(false)
const inputRef = ref()
const drawerControllerStore = useDrawerControllerStore()
const { mode } = storeToRefs(drawerControllerStore)

watch(
  () => props,
  (newVal) => {
    curLecture.value = { ...(newVal.lecture as lectureType[]) }
    lectureIndex.value = newVal.index
    stepMode.value = newVal.step
    // console.log('lecture: ' + JSON.stringify(curLecture.value, null ,2));
  },
  { deep: true, immediate: true }
)
const change2ReadMode = () => {
  if (stepMode.value == 2) {
    isEditing.value = false
  }
}
const change2EditMode = () => {
  if (stepMode.value == 2) {
    isEditing.value = true
    inputRef.value.focus()
  }
}

// const handleSaveContent = () => {
//   if (curLecture.value.content.length > 70) {
//     ElMessage.error("文本过长")
//     return
//   }
//   const param: params4updateVideoLecture = {
//     beginning: curLecture.value.beginning,
//     id: curLecture.value.id,
//     // 改内容
//     type: 2,
//     updateManuscript: curLecture.value.content,
//   }
//   updateVideoLecture(param).then((res) => {
//     if (res.success) {
//       ElMessage.success("编辑成功")
//       isEditing.value = false
//     } else {
//       ElMessage.error("保存失败")
//     }
//   })
// }

// const handleChangeHead = () => {
//   const param: params4updateVideoLecture = {
//     beginning: curLecture.value.beginning,
//     id: curLecture.value.id,
//     type: 0,
//     updateManuscript: "",
//   }
//   updateVideoLecture(param).then((res) => {
//     if (res.success) {
//       ElMessage.success("修改段首成功")
//       isEditing.value = false
//     } else {
//       ElMessage.error("修改段首失败")
//     }
//   })
// }
</script>

<template>
  <!-- 
    讲稿区域 每一行是一个
   -->
  <div class="lecture-line-wrapper">
    <span class="time-wrapper">
      {{ curLecture[0].startTime }}
    </span>
    <div class="main-wrapper">
      <span
        class="content-wrapper"
        @click="change2EditMode"
        v-show="!isEditing"
      >
        <template v-if="stepMode == 3">
          <!-- {{ curLecture }} -->
          <span class="line-block">
            <div v-for="line in curLecture" class="lineWordContent renderItem">
              <span class="ck-content" v-html="processAllLatexEquations(line.caption)"></span>
            </div>
          </span>
        </template>
      </span>
    </div>
  </div>
</template>

<style scoped>
.lecture-line-wrapper {
  width: 100%;
  overflow: hidden;
  flex-wrap: wrap;
  font-weight: 400;
  display: flex;
  flex-direction: column;
  min-height: 46px;
  align-items: flex-start;
  padding: 5px 0 35px;
  margin-left: 10px;
  font-family: var(--text-family);

  &.selectable {
    .time-wrapper {
      line-height: 22px;
      justify-content: center;
    }
    .main-wrapper {
      background-color: white;
      font-size: 12px;
      .content-wrapper {
        width: 864px;

        /*overflow: hidden;*/
        /*text-overflow: ellipsis;*/
        /*white-space: nowrap;*/
      }
      .select-wrapper {
        display: flex;
        width: 96px;
        justify-content: center;
        .el-checkbox {
          height: 22px;
        }
      }
    }
  }
  &:not(.selectable) {
    .time-wrapper {
      display: flex;
      line-height: 22px;
      justify-content: flex-start;
      font-size: 12px;
      /*overflow: hidden;*/
      /*text-overflow: ellipsis;*/
      /*white-space: nowrap;*/
    }
    .main-wrapper {
      border-radius: 3px;
      background-color: white;
      padding: 15px 10px 0;
      width: 98%;
      font-size: 14px;
      display: flex;
      justify-content: flex-start;
      .line-block {
        width: 100%;
        display: flex;
        flex-direction: column;
      }
      .content-wrapper {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;
      }
    }
  }
}
.head {
  background-color: var(--color-light);
}
</style>
