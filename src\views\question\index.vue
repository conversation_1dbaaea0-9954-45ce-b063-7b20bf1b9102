<script setup lang="ts">
import { onMounted, onUnmounted, onUpdated, ref, watch } from "vue";
import {
  getMessageListApi,
  type getMessageListParams,
} from "@/apis/path/message";
import { type questionType } from "@/utils/type";
import { ElMain, ElMessage } from "element-plus";
import { useRouterPushStore } from "@/stores/routerPushStore";
import router from "@/router";
import { emitter } from "@/utils/emitter";
import { Event } from "@/types/event";
import { processAllLatexEquations } from '@/utils/latexUtils';
const routerPushStore = useRouterPushStore();
const selectIndex = ref(0);
const messageList = ref<questionType[]>([]);
const messageNoAnsList = ref<questionType[]>([]);
const currentPage = ref(1);
const currentNoAnsPage = ref(1);
const noMore = ref(false);
const loading = ref(true);
const params = ref<getMessageListParams>({
  current: 1,
  limit: 10,
  selected: 0, // 1---没看过的   0---所有
});
let intersectionObserver;

// watch该变观察状态
watch(noMore, (newVal) => {
  if (newVal) {
    intersectionObserver.disconnect();
  } else {
    const foot = document.querySelector(".scrollerFooter");
    if (foot !== null) {
      intersectionObserver.observe(foot);
    }
  }
});
// 加载下一页
const loadItems = () => {
  if (params.value.selected === 0) {
    getMsgList(currentPage.value);
    currentPage.value++;
  } else if (params.value.selected === 1) {
    getMsgList(currentNoAnsPage.value);
    currentNoAnsPage.value++;
  }
};

// 设置获取列表参数
const setParams = (current: number) => {
  params.value.current = current;
  params.value.limit = 10;
  params.value.selected = selectIndex.value;
};

// 处理选择=> 全部/未看
const changeSelect = (index: number) => {
  loading.value = true;
  if (index == selectIndex.value) {
    return;
  }
  selectIndex.value = index;
  currentPage.value = 1;
  currentNoAnsPage.value = 1;
  noMore.value = false;

  getMsgList(1);
};
// 点击跳转
const toQuestion = (ques: any) => {
  routerPushStore.setToOpenQuesId(ques.questionId);
  if (ques.belongProjectType === 5 || ques.belongProjectType === 6) {
    router.push(`/prjdetail?uniqueCode=${ques.uniqueCode}`);
  } else {
    router.push(`/exerdetail?uniqueCode=${ques.uniqueCode}`);
  }
};
// 获取MsgList
const getMsgList = (page: number) => {
  setParams(page);
  getMessageListApi(params.value)
    .then((res) => {
      // @ts-ignore
      if (res.success) {
        loading.value = false;
        if (res.data.records.length === 0) noMore.value = true;
        if (params.value.selected === 0) {
          if (page === 1) {
            messageList.value = res.data.records;
          } else {
            messageList.value.push(...res.data.records);
          }
        } else if (params.value.selected === 1) {
          if (page === 1) {
            messageNoAnsList.value = res.data.records;
          } else {
            messageNoAnsList.value.push(...res.data.records);
          }
        }
      } else {
        loading.value = false;
        ElMessage.error("获取失败");
      }
    })
    .catch((err) => {
      console.log("[debug]:err", err);
    });
};
// 开启滚动条观察
const startWatchScrollerFooter = (observer: any) => {
  // 注册观察者
  observer = new IntersectionObserver(function (entries) {
    // console.log("entries: ", entries)
    // 如果不可见，就返回
    if (entries[0].intersectionRatio <= 0) return;
    loadItems();
    // console.log("Loaded new items")
  });

  // 开始观察
  const foot = document.querySelector(".scrollerFooter");
  if (foot !== null) {
    observer.observe(foot);
  }
};
const endWatchScrollerFooter = (observer: any) => {
  if (observer) {
    observer.disconnect();
  }
};
const handleRefreshMessage = () => {
  router.go(0);
};
onMounted(() => {
  emitter.on(Event.REFRESH_LIST, handleRefreshMessage);
  startWatchScrollerFooter(intersectionObserver);
});

// 断开所有观察
onUnmounted(() => {
  emitter.off(Event.REFRESH_LIST, handleRefreshMessage);
  endWatchScrollerFooter(intersectionObserver);
});
</script>

<template>
  <div class="message">
    <div class="head">
      <div class="title">我发布的项目中收到的提问和回复</div>
      <div class="select">
        <div
          class="btn"
          :class="selectIndex == 0 ? 'active' : 'inactive'"
          @click="changeSelect(0)"
        >
          查看全部
        </div>
        <div
          class="btn"
          :class="selectIndex == 1 ? 'active' : 'inactive'"
          @click="changeSelect(1)"
        >
          只看我未回复过的
        </div>
      </div>
    </div>
    <div class="content">
      <el-skeleton
        style="width: 100%"
        :loading="loading"
        animated
        :throttle="500"
        v-for="item in 6"
      >
        <template
          #template
          style="
            display: flex;
            align-items: center;
            justify-content: space-between;
          "
        >
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-top: 30px;
              height: 90px;
            "
          >
            <span style="width: 95%; margin-right: 20px; margin-left: 20px">
              <el-skeleton-item variant="text" style="width: 15%" />
              <el-skeleton-item variant="text" style="margin-bottom: 10px" />
              <el-skeleton-item variant="text" style="width: 20%" />
            </span>
            <span style="margin-right: 10px">
              <el-skeleton-item
                variant="image"
                style="width: 120px; height: 90px"
              />
            </span>
            <span style="width: 17%">
              <el-skeleton-item variant="text" style="width: 70%" />
            </span>
          </div>
          <div
            style="
              height: 1px;
              width: 100%;
              background-color: #dcdfe6;
              margin-top: 10px;
            "
          ></div>
        </template>
      </el-skeleton>
      <div
        class="content-item"
        v-for="ques in params.selected === 0 ? messageList : messageNoAnsList"
        :key="ques.questionId"
        v-if="!loading"
        :class="ques.isSelect === 1 ? 'selected-item' : ''"
        @click="toQuestion(ques)"
      >
        <div class="left-info">
          <div class="user">
            <span class="username">
              {{ ques.showName }}
            </span>
            向你提问：
          </div>
          <div class="question-content">
            【<span
              class="content-text questionList"
              v-html="processAllLatexEquations(ques.keyword)"
            ></span
            >】
            <span style="min-width: 100px; margin-left: 15px">{{
              ques.questionType
            }}</span>
          </div>
          <div class="date">{{ ques.createTime }}</div>
        </div>
        <div class="right-prj">
          <div class="prj-cover" v-if="ques.coverPic">
            <img :src="ques.coverPic" style="width: 120px; height: 90px" />
          </div>
          <div class="prj-intro">
            <span class="questionList" v-html="processAllLatexEquations(ques.title)"></span>
          </div>
        </div>
      </div>
      <div v-if="noMore" class="nomore">No More</div>
      <div class="scrollerFooter"></div>
    </div>
  </div>
</template>

<style scoped lang="less">
.nomore {
  margin-top: 5px;
  display: grid;
  place-items: center;
  font-family: var(--text-family);
  color: var(--color-primary);
}
.message {
  margin: 20px auto auto auto;
  width: 1300px;
  height: 100%;
  background-color: #fff;
  padding: 16px 20px;
  box-sizing: border-box;
  overflow: hidden;
  .head {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .title {
      font-size: 16px;
      font-weight: 600;
      color: var(--color-primary);
    }

    .select {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
    }
    .btn:first-child {
      margin-right: 10px;
    }

    .btn {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
      width: 160px;
      height: 35px;
      border-radius: 5px;
      border: solid 1px var(--color-primary);
      &:hover {
        cursor: pointer;
      }
    }

    .active {
      background-color: var(--color-primary);
      color: white;
    }

    .inactive {
      background-color: white;
      color: var(--color-primary);
      &:hover {
        background-color: #d6e9f6;
        font-weight: 800;
      }
    }
  }
  .content {
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-top: 10px;
    height: calc(100% - 50px); /* 减去head的高度 */
    overflow-y: auto;
    &::-webkit-scrollbar {
      position: absolute;
      /*滚动条整体样式*/
      width: 5px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      background: var(--color-grey);
    }
    .selected-item {
      background: linear-gradient(
        to bottom,
        #d6e9f6 0%,
        #d6e9f6 94%,
        rgba(255, 255, 255, 1) 60%
      );
    }
    .content-item {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      border-bottom: solid 1px #dcdfe6;
      padding: 20px 20px;
      box-sizing: border-box;
      height: 140px;
      &:hover {
        cursor: pointer;
      }
      // background: linear-gradient(to bottom, #d6e9f6 0%, #d6e9f6 94%, rgba(255, 255, 255, 1) 60%);
      .left-info {
        display: flex;
        flex-direction: column;
        font-size: 14px;
        font-weight: 400;
        max-width: 100%;
        .user {
          margin-bottom: 10px;
          .username {
            font-size: 14px;
            font-weight: 600;
            margin-right: 10px;
          }
        }

        .question-content {
          margin-bottom: 11px;
          display: flex;
          overflow: hidden;
          max-width: 100%;
          width: auto;

          .content-text {
            height: 40px;
            // display: flex;
            // flex-direction: column;
            width: 100%;
            max-width: 900px;
          }
          :deep(.content-text p) {
            display: flex;
            overflow: hidden;
            flex-wrap: nowrap;
            text-overflow: ellipsis;
          }
        }

        .date {
          font-size: 12px;
          color: #666;
        }
      }

      .right-prj {
        display: flex;
        flex-direction: row;
        align-items: center;
        .prj-cover {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 120px;
          height: auto;
          background-color: var(--color-grey);
          margin-bottom: 5px;
        }

        .prj-intro {
          width: 160px;
          font-size: 14px;
          font-weight: 600;
          word-break: break-all;
          margin-left: 6px;
          display: flex;
          overflow: hidden;
          max-height: 80px;
          :deep(p) {
            display: inline;
          }
        }
      }
    }
    .content-item:not(:first-child) {
      margin-top: 7px;
    }
  }
}
</style>
