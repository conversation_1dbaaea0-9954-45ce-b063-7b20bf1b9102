<script setup lang="ts">
import PicIcon from './assets/images/pic_icon.jpg';
import TableIcon from './assets/images/table_icon.jpg';
import CodeIcon from './assets/images/common/terminal.png';
import ThumbNail from './components/ThumbNail.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, onUnmounted, onUpdated, provide, ref, watch } from 'vue';
import { RouterView, useRouter } from 'vue-router';
import { useDrawerControllerStore } from './stores/drawerController';
import { handleLineWord } from './utils/lineWord2V2';
import { useElementStore } from './stores/drawerElement';
import { getExerDetailApi } from '@/apis/path/exercise';
import { emitter } from './utils/emitter';
import { getProjectDetailApi } from '@/apis/path/prjdetail';
import { Event } from './types/event';
import { useRouterPushStore } from './stores/routerPushStore';
import { intersection } from 'lodash-es';
import { userInfoStore } from '@/stores/userInfo';
import { convertMathTagsToMDLatex } from '@/utils/latexUtils';
import { bindKeyWordsHover } from '@/composables/useKeyWordsHover';

const routerPushStore = useRouterPushStore();
const elStore = useElementStore();
const drawerControllerStore = useDrawerControllerStore();
const thumbNailElement = ref<HTMLElement | null>(null);
// 窗口多开处理逻辑
const router = useRouter();
const thumbnail = ref();
const userinfo = userInfoStore();

document.addEventListener('visibilitychange', () => {
  if (!document['hidden']) {
    userinfo.getUserInfo();
    console.log('出现');
  } else {
    //隐藏
    console.log('隐藏');
  }
});
const imgs: Array<HTMLElement> = [];
// @ts-ignore
const observer = new MutationObserver((mutationsList) => {
  let scripts = Array.prototype.slice.call(document.body.getElementsByTagName('script'));
  scripts.forEach(function (script) {
    if (!script.type || !script.type.match(/math\/tex/i)) {
      return -1;
    }
    const display = script.type.match(/mode\s*=\s*display(;|\s|\n|$)/) != null;

    // 创建公式元素
    const katexElement = document.createElement(display ? 'div' : 'span');
    katexElement.setAttribute('class', display ? 'equation' : 'inline-equation');
    katexElement.setAttribute('latexCode', script.text);
    try {
      // @ts-ignore
      katex!.render(script.text.replace(/\s+/g, ' '), katexElement, {
        displayMode: display,
        throwOnError: false,
        output: 'html'
      });
    } catch (err) {
      //console.error(err); linter doesn't like this
      katexElement.textContent = script.text;
    }

    // 创建包装span元素，添加data-qid和data-index属性
    const wrapperSpan = document.createElement('span');
    wrapperSpan.setAttribute('data-qid', '');
    wrapperSpan.setAttribute(
      'data-index',
      (document.querySelectorAll('[data-index]').length + 1).toString()
    );
    wrapperSpan.setAttribute('class', 'highlight');

    // 将公式元素添加到包装span中
    wrapperSpan.appendChild(katexElement);

    // 替换原始script标签
    script.parentNode.replaceChild(wrapperSpan, script);
  });

  const images = document.querySelectorAll('img');

  images.forEach((img) => {
    if (imgs.includes(img)) {
      return;
    }
    img.addEventListener(
      'dblclick',
      function () {
        const range = document.createRange();
        range.selectNode(this);

        const selection = window.getSelection();
        selection!.removeAllRanges();
        selection!.addRange(range);
      },
      true
    );
    imgs.push(img);
  });
  const questionLists = document.querySelectorAll('.questionList, .questionPop');
  questionLists.forEach((questionList) => {
    const figureElements = questionList.querySelectorAll('figure:not([thumbnail])');
    figureElements?.forEach((figure) => {
      const imgElements = figure.querySelectorAll('img:not([thumbnail])');
      imgElements?.forEach((item) => {
        const newImg = document.createElement('img');
        newImg.src = PicIcon;
        newImg.style.height = '14px';
        newImg.style.minWidth = '16px';
        // @ts-ignore
        item.style.width = '155px';
        // @ts-ignore
        item.style.height = 'auto';
        newImg.setAttribute('thumbnail', item.outerHTML);
        newImg.addEventListener('mouseover', (event) => {
          thumbNailElement.value = event.target as HTMLElement;
        });
        figure.replaceWith(newImg);
      });
      const tableElements = figure.querySelectorAll('table:not([thumbnail])');
      // @ts-ignore
      tableElements?.forEach((item) => {
        const newImg = document.createElement('img');
        newImg.src = TableIcon;
        newImg.style.height = '14px';
        newImg.style.minWidth = '16px';
        newImg.setAttribute('thumbnail', figure.outerHTML);
        newImg.addEventListener('mouseover', (event) => {
          thumbNailElement.value = event.target as HTMLElement;
        });
        figure.replaceWith(newImg);
      });
    });
    const imgElements = questionList.querySelectorAll('img:not([thumbnail])');
    imgElements?.forEach((item) => {
      const newImg = document.createElement('img');
      newImg.src = PicIcon;
      newImg.style.height = '14px';
      newImg.style.minWidth = '16px';
      for (let i = item.attributes.length - 1; i >= 0; i--) {
        const attribute = item.attributes[i];
        if (attribute.name !== 'src') {
          item.removeAttribute(attribute.name);
        }
      }
      // @ts-ignore
      item.style.width = '300px';
      // @ts-ignore
      item.style.height = 'auto';
      newImg.setAttribute('thumbnail', item.outerHTML);
      newImg.addEventListener('mouseover', (event) => {
        thumbNailElement.value = event.target as HTMLElement;
      });
      item.replaceWith(newImg);
    });
    const codeElements = questionList.querySelectorAll('pre:not([thumbnail])');
    codeElements?.forEach((item) => {
      const newImg = document.createElement('img');
      newImg.src = CodeIcon;
      newImg.style.minWidth = '16px';
      newImg.style.height = '14px';
      newImg.setAttribute('thumbnail', item.outerHTML);
      newImg.addEventListener('mouseover', (event) => {
        thumbNailElement.value = event.target as HTMLElement;
      });
      item.replaceWith(newImg);
    });
  });
  // 处理 keyWords 区域的悬浮放大功能
  const keyWordsContainers = document.querySelectorAll('.keyWords');
  keyWordsContainers.forEach((container) => {
    const elements = container.querySelectorAll('.equation, img');
    elements.forEach((element) => {
      bindKeyWordsHover(element as HTMLElement);
    });
  });
  // 为公式元素添加事件监听器
  // addFormulaEventListeners();
});

/**
 * 获取带有data-qid的元素，优先处理公式元素
 * @param target 目标元素
 * @returns 带有data-qid的元素或null
 */
const getElementWithQid = (target: HTMLElement): HTMLElement | null => {
  let el: HTMLElement | null = null;
  let isFormulaElement = false;

  // 检查是否是公式元素
  if (target.closest("[class^='inline-equation'], [class^='equation']")) {
    el = target.closest("[class^='inline-equation'], [class^='equation']") as HTMLElement;
    isFormulaElement = true;
  } else {
    el = target;
  }

  // 先尝试获取带data-qid的父元素
  let element = el.closest('[data-qid]') as HTMLElement;

  // 只有当是公式元素且没找到data-qid时，才查找内部子元素
  if (!element && isFormulaElement && el) {
    const childrenWithQid = el.querySelectorAll('[data-qid]');
    if (childrenWithQid.length > 0) {
      element = childrenWithQid[0] as HTMLElement;
    }
  }

  return element;
};

/**
 * 处理文本点击事件
 * @param e 鼠标点击事件对象
 */
const handleWord = (e: MouseEvent) => {
  // 提问模式禁止点击
  if (drawerControllerStore.mode) return;

  // 检查点击的是否是新元素
  if (elStore.element != e.target) {
    const element = getElementWithQid(e.target as HTMLElement);

    // 只有当找到了带有data-qid的元素时才触发点击事件
    if (element) {
      // 触发点击单词事件
      emitter.emit(Event.CLICK_WORD, element);
    } else {
      console.log('点击的元素没有关联的问题ID');
    }
  }
};

const lineWordContent = ref('');
const isQuestionDrawerOpen = ref(false);
watch(
  () => lineWordContent.value,
  (newVal) => {
    if (newVal) {
      isQuestionDrawerOpen.value = true;
    } else {
      // 当lineWordContent被清空时，可能是弹窗关闭了
      // 立即重置状态，不使用延迟
      isQuestionDrawerOpen.value = false;
    }
  }
);
// 划词时获取文本，打开弹窗
const watchLineWordBehaviour = () => {
  // 如果不是提问模式或者弹窗已经打开，则不执行划词功能
  if (!drawerControllerStore.mode || isQuestionDrawerOpen.value) return;

  // 获取选中的文本
  const selectedText = handleLineWord() as string;

  // 当处于提问模式时，使用convertMathTagsToMDLatex处理选中的文本
  if (drawerControllerStore.mode && selectedText) {
    // 处理选中的文本中的数学标签
    lineWordContent.value = convertMathTagsToMDLatex(selectedText);
  }
  console.log('linecontent', lineWordContent.value);
};

router.beforeEach((to, from, next) => {
  // 检查是否跳转到 /exerdetail
  const uniqueCode = to.query.uniqueCode as string;
  if (to.path === '/exerdetail') {
    getExerDetailApi(uniqueCode).then((res) => {
      if (res.success) {
        routerPushStore.setData(res.data);
        next();
      } else {
        next(false);
        ElMessage.warning(res.message);
        if (from.path === '/exercise' || from.path === '/question') {
          emitter.emit(Event.REFRESH_LIST, true);
        }
      }
    });
  } else if (to.path === '/prjdetail') {
    getProjectDetailApi(uniqueCode).then((res) => {
      if (res.success) {
        routerPushStore.setData(res.data.list[0]);
        next();
      } else {
        next(false);
        ElMessage.warning(res.message);
        if (from.path === '/question') {
          emitter.emit(Event.REFRESH_LIST, true);
        }
      }
    });
  } else {
    next();
  }
});

// 定义一个函数，用于处理公式元素的mouseover事件
const handleFormulaHover = (event: MouseEvent) => {
  // 获取公式元素
  const formulaElement = event.currentTarget as HTMLElement;

  // 检查公式内部是否有data-qid元素
  const hasQidInside = formulaElement.querySelector('[data-qid]');
  if (hasQidInside) {
    // 添加类名
    formulaElement.classList.add('highlightHover');
  }
};

// 定义一个函数，用于处理公式元素的mouseout事件
const handleFormulaOut = (event: MouseEvent) => {
  // 获取公式元素
  const formulaElement = event.currentTarget as HTMLElement;

  // 移除类名
  formulaElement.classList.remove('highlightHover');
};

// 重新定义MutationObserver回调，处理新添加的公式元素
const addFormulaEventListeners = () => {
  const formulaElements = document.querySelectorAll('.inline-equation, .equation');
  formulaElements.forEach((formula) => {
    // 检查是否已添加过事件监听器
    if (!formula.hasAttribute('data-hover-bound')) {
      // 添加mouseover事件
      formula.addEventListener('mouseover', handleFormulaHover);

      // 添加mouseout事件
      formula.addEventListener('mouseout', handleFormulaOut);

      // 标记已添加过事件监听器
      formula.setAttribute('data-hover-bound', 'true');
    }
  });
};

onMounted(() => {
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  document.addEventListener('mouseup', watchLineWordBehaviour);

  // 监听抽屉关闭事件，重置lineWordContent
  emitter.on('clear_line_word_content', () => {
    lineWordContent.value = '';
    isQuestionDrawerOpen.value = false;
  });
});

onUnmounted(() => {
  observer.disconnect();
  // @ts-ignore
  window.handleWord = null;
  document.removeEventListener('mouseup', watchLineWordBehaviour);
  // 移除事件监听器
  emitter.off('clear_line_word_content');
});
// // 获取文档中所有的 <code> 标签
// const codeElements = document.querySelectorAll('code');

// // 遍历所有 <code> 标签并添加 hljs 类
// codeElements.forEach((codeElement) => {
//   codeElement.classList.add('hljs');
// });
provide('handleWord', handleWord);
provide('lineWordContent', lineWordContent);
</script>

<template>
  <router-view></router-view>
  <ThumbNail ref="thumbnail" v-model="thumbNailElement"></ThumbNail>
</template>

<style scoped lang="less">
:deep(.el-drawer__header) {
  margin-bottom: 0;
}

// :deep(.highlight) {
//   color: var(--color-primary);
//   cursor: pointer;
// }
:deep(.highlightHover) {
  text-decoration: underline; /* 普通文本使用下划线 */
}
/* 公式元素使用底边框 */
:deep(.inline-equation.highlightHover),
:deep(.equation.highlightHover) {
  /* 确保没有其他边框相关样式 */
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  border-bottom: 1px solid var(--el-color-primary) !important;
}

/* 强制清除公式内部元素的下划线 */
:deep(.inline-equation.highlightHover *),
:deep(.equation.highlightHover *) {
  text-decoration: none !important;
}

:deep(.highlightHover img) {
  border: 2px solid var(--el-color-primary); /* 添加边框 */
}
:deep(.ck.ck-balloon-panel.ck-powered-by-balloon) {
  display: none;
}
</style>

<style>
.tooltip-width {
  max-width: 500px;
}

.el-drawer__header {
  padding: 20px 10px  0 !important;
}

.el-drawer__body {
  padding: 20px 10px 0 !important;
}
</style>