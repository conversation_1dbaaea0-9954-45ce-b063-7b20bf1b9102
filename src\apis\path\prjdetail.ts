import { http } from "../index";
import { APIResponse } from "@/utils/type";

export interface params2GetSecDetail {
    sectionId: number,
    uniqueCode: number
}
export interface params2GetQuesList {
    chapterId?: number,
    uniqueCode: string,
    limit: number,
    current: number,
    questionStatus: number,
    questionType: string,
    questionNecessity: number,
    questionWeight: number,
    keyword: string,
}
export interface params2DelQues {
    questionId: number,
}

export interface params2EditQues {
    qid: number,
    keyword: string,
    associatedWords: string,
    questionType: string,
    questionNecessity: number,
    questionWeight: number,
    questionDescription?: string,
}
export interface param2GetExamList {
    contentId: number,
    current: number,
    limit: number,
}
// 获取项目详情
export function getProjectDetailApi(uniqueCode: string): Promise<APIResponse> {
    return http.get(`/tprj/getPrjDetail?uniqueCode=${uniqueCode}`)
}

// 获取问题列表
export function getQuesListApi(params: params2GetQuesList): Promise<APIResponse> {
    return http.post(`/question/all/query`, params)
}
// 删除问题
export function deleteQuestionApi(params: params2DelQues): Promise<APIResponse> {
    return http.post(`/question/deleteQuestion`, params)
}
// 编辑问题
export function editQuestionApi(params: params2EditQues): Promise<APIResponse> {
    return http.post(`/question/edit`, params)
}
// 获取问题详情
export function getQuestionApi(questionId: string): Promise<APIResponse> {
    return http.get(`/answer/getAnswerList?questionId=${questionId}`)
}
// 获取知识点列表
export function getKlgListApi(name: string, type: number): Promise<APIResponse> {
    return http.get(`/answer/getKlgList?name=${name}&type=${type}`)
}
// 获取答案列表/问题详情
export function getAnswersApi(questionId: string): Promise<APIResponse> {
    return http.get(`/answer/getAnswers?questionId=${questionId}`)
}
// 获取测评详情
export function getExamDetailApi(contentId: number): Promise<APIResponse> {
    return http.get(`/twords/getExamContent?contentId=${contentId}`)
}
// 奇怪的bug
export function getSectionDetailApi(params: params2GetSecDetail): Promise<APIResponse> {
    return http.post(`/tprj/getSectionDetail`, params)
}

//获取每个问题的全部答案（分页）
export function getAnswerListByQuestionId(questionId: number, current: number, limit: number, answerExplanation?: string, answerStatus?: number,): Promise<APIResponse> {
    return http.post(`/question/answer/page`, { questionId, answerExplanation, answerStatus, current, limit });
}

//批量删除问题
export function deleteMultipleQuestionApi(questionIds: [string]): Promise<APIResponse> {
    return http.post(`/question/batch/delete`, {questionIds})
}