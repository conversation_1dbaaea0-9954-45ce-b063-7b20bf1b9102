import { createApp } from "vue";
import { createPinia } from "pinia";
import piniaPersist from "pinia-plugin-persist";
// import '@/assets/theme/index.scss'
import App from "./App.vue";
import "element-plus/dist/index.css";
import "highlight.js/styles/default.css";
import ElementPlus from "element-plus";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import router from "./router/index";
// import CKEditor from "@ckeditor/ckeditor5-vue";
import "./assets/main.css";
import "@endlessorigin/select_to_ask/dist/index.css";
import "katex/dist/katex.min.css";

const pinia = createPinia();
pinia.use(piniaPersist);
const app = createApp(App);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
app.use(pinia);
// app.use(CKEditor);

app.use(router).use(ElementPlus).mount("#app");
