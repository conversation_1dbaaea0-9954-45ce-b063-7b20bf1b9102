<template>
    <div class="main-wrapper">
        <div class="top">
            <span class="title ck-content" v-html="(title)" v-if="fromType === '1'"></span>
            <span class="title2 ck-content" v-html="(title)" v-else></span>
            <span @click="goBack" class="back-btn">返回</span>
        </div>
        <div class="middle">
            <div class="user"><span class="username">{{ curQuestion.userName }} </span><span> 的提问</span></div>
            <div class="question" v-if="qTypeDict[curQuestion.questionType] !== qType.open">
                <span>【{{ curQuestion.keyword }}】 </span><span>{{ curQuestion.questionType }}?</span>
            </div>
            <div class="question-type">
                <div class="necessity" v-if="curQuestion.questionNecessity == 1">必要</div>
                <div class="necessity" v-if="curQuestion.questionNecessity == 2">参考</div>
                <div class="weight" v-if="curQuestion.questionWeight == 1">个人</div>
                <div class="weight" v-if="curQuestion.questionWeight == 2">公开</div>
            </div>
            <div class="time">
                <span>{{ curQuestion.createTime }}</span>
            </div>
        </div>
        <div class="bottom">
            <form-switch :need-ans-mode="true" :need-search-ans="true" @selectStatus="handleSelect"
                @searchList="handleSearchList" placeholder="请输入项目标题"></form-switch>
            <div class="line"></div>
            <div class="content-container">
                <el-table ref="answerTableRef" :data="tableData" class="table" empty-text="暂无数据"
                    :row-style="{ height: '46px', overflow: 'hidden' }"
                    :cell-style="{ height: '46px', width: '100%', overflow: 'hidden', maxHeight: '46px' }"
                    :style="{ tableLayout: 'fixed' }"
                    :header-cell-style="{ height: '46px', 'font-weight': 600, 'font-size': '14px', 'color': '#333333' }">
                    <el-table-column type="index" :index="indexMethod" label="序号" width="55">
                    </el-table-column>
                    <el-table-column class-name="ellipsis-column" prop="answerExplanation" label="答案解释" width="270"
                        header-align="center">
                        <template #default="scope">
                            <div class="ellipsis-cell">
                                <el-tooltip placement="top" :show-after="200" popper-class="tooltip-width">
                                    <template #content>
                                        <div class="tooltip-content"
                                            v-html="(scope.row.answerExplanation)"></div>
                                    </template>
                                    <div class="ellipsis-text flex-start"
                                        v-html="(scope.row.answerExplanation)"></div>
                                </el-tooltip>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="selectedKnowledge" label="已选知识数" width="100" align="center">
                    </el-table-column>
                    <el-table-column prop="pendingKnowledge" label="待处理知识数" width="130" align="center"></el-table-column>
                    <el-table-column prop="createTime" label="回答时间" width="180" align="center"></el-table-column>
                    <el-table-column label="回答人" width="95" align="center">
                        <template #default="{ row }">
                            <div class="ellipsis-cell">
                                <el-tooltip placement="top" :show-after="200" popper-class="tooltip-width">
                                    <template #content>
                                        <div class="tooltip-content">{{ row.responder }}</div>
                                    </template>
                                    <div class="ellipsis-text">{{ row.responder }}</div>
                                </el-tooltip>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="答案状态" width="100" align="center">
                        <template #default="{ row }">
                            <span v-if="row.releaseStatus == 0">未发布</span>
                            <span v-if="row.releaseStatus == 1">已发布</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="210" align="center">
                        <template #default="{ row }">
                            <span class="operationBlock">
                                <span :class="row.releaseStatus === 0 && row.answerStatus === ansMode.completed ? 'operationBtn' : 'disabledBtn'"
                                    @click="handlePublishAns(row)">发布</span>
                                <span :class="row.releaseStatus === 0 ? 'operationBtn' : 'disabledBtn'" @click="handleEditQues(row)">编辑</span>
                                <span :class="row.answerStatus !== ansMode.processing ? 'operationBtn' : 'disabledBtn'"
                                    @click="handleDeleteAns(row)">删除</span>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
                <my-flipper @change-page="handleChangePage" :current="currentPage" :page-size="pageSize"
                    :total="total"></my-flipper>
            </div>
        </div>
    </div>
    <EditDialog v-if="dialogVisible"></EditDialog>
</template>

<script setup lang="ts">
import { getAnswerListByQuestionId, getAnswersApi, getProjectDetailApi } from '@/apis/path/prjdetail';
import { onMounted, reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { processAllLatexEquations } from '@/utils/latexUtils';
import router from '@/router';
import { answer, questionItem } from '@/utils/type';
import { qMode, qWeight, qType, qTypeDict, qModeDict, qWeightDict, ansModeDict, ansMode } from '@/utils/constant';
import { userInfoStore } from '@/stores/userInfo';
import { findKeyByValue } from '@/utils/func';
import { deleteAnswerApi, getAnswerDetailApi, getAnswerListApi, params2DelAns, params2getAnsList, publishAnswerApi } from '@/apis/path/answerList';
import { ElMessage, ElMessageBox } from 'element-plus';
import EditDialog from "@/components/EditDialog.vue"
import { useDialogStore } from '@/stores/dialog';
import { getExerDetailApi } from '@/apis/path/exercise';

const route = useRoute()
const dialogStore = useDialogStore()
const dialogVisible = ref(false)

watch(
    () => dialogStore.editDialogVisible,
    (newVal) => {
        dialogVisible.value = newVal
    }
)

const uniqueCode = route.query.uniqueCode as string;
const questionId = route.query.questionId as string;
const fromType = route.query.type as string

const title = ref('')
const curQuestion = reactive<questionItem>({
    questionId: -1,
    questionDescription: '',
    associatedWords: '',
    keyword: '',
    questionType: '是什么',
    questionNecessity: qMode.ness,
    questionWeight: qWeight.open,
    userName: '',
    createTime: '',
    answerNumber: 0,
    questionState: '',
    canDelete: true
});

const tableData = ref([]); // 表格数据

const currentPage = ref(1); // 当前页
const pageSize = ref(10); // 每页大小
const total = ref(0); // 总数

watch(
    () => dialogStore.refresh,
    (newVal) => {
        if (newVal) {
            getNewAnsList(currentPage.value, params.value.answerExplanation, params.value.answerStatus)
            dialogStore.refresh = false
        }
    }
)

const goBack = () => {
    if (fromType === '1') {
        router.push(`/prjdetail?uniqueCode=${uniqueCode}`)
    } else if (fromType === '2') {
        router.push(`/exerdetail?uniqueCode=${uniqueCode}`)
    }
}

// 处理选择 => formSwitch
const handleSelect = (status: number) => {
    getNewAnsList(1, params.value.answerExplanation, status);
};
// 处理搜索 => formSwitch
const handleSearchList = (ansExp: string) => {
    getNewAnsList(1, ansExp, params.value.answerStatus);
};

const params = ref({
    current: currentPage.value,
    limit: pageSize.value,
    answerStatus: ansMode.default,
    answerExplanation: '',
});
// 获取答案列表
const getNewAnsList = async (newPage?: number, ansExp?: string, status?: number) => {
    if (newPage) params.value.current = newPage;
    params.value.answerExplanation = ansExp;

    // 仅当 status 是 0、1、2 时才设置参数，-1 时不传递
    if (status === 0 || status === 1 || status === 2) {
        params.value.answerStatus = status;
    } else if (status === -1) {
        // 若 status 为 -1，删除该参数（避免传递）
        delete params.value.answerStatus;
    }

    // 调用接口时，参数会根据上面的逻辑自动处理
    const res: any = await getAnswerListByQuestionId(
        Number(questionId),
        params.value.current,
        params.value.limit,
        params.value.answerExplanation,
        params.value.answerStatus // 当 status=-1 时，此处为 undefined，接口不会传递该参数
    );

    if (res.success) {
        const data = res.data;
        tableData.value = data.records;
        total.value = data.total;
    }
};

// 计算列表index
const indexMethod = (index: number) => {
    return (currentPage.value - 1) * pageSize.value + index + 1;
};

// 处理发布答案
const handlePublishAns = (ans: any) => {
    if (ans.answerStatus !== ansMode.completed) return;
    if (ans.releaseStatus === 1) return;
    publishAnswerApi(ans.answerId).then((res) => {
        if (res.success) {
            ElMessage.success('发布成功');
            getNewAnsList(currentPage.value, params.value.answerExplanation, params.value.answerStatus);
        }
    });
};

// 处理编辑答案
const handleEditQues = (row: any) => {
    if (row.releaseStatus === 1) return;
    dialogStore.answerId = row.answerId
    dialogStore.questionId = curQuestion.questionId
    dialogStore.editDialogVisible = true
};

// 处理删除答案
const handleDeleteAns = (ans: answer) => {
    if (ans.answerStatus === ansMode.processing) return;
    ElMessageBox.confirm('确定删除答案吗？', '删除答案', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    })
        .then(() => {
            const params2 = ref<params2DelAns>({
                answerId: ans.answerId
            });
            deleteAnswerApi(params2.value).then((res) => {
                if (res.success) {
                    getNewAnsList(currentPage.value, params.value.answerExplanation, params.value.answerStatus);
                    ElMessage.success('删除成功');
                } else {
                    ElMessage.warning(res.message);
                }
            });
        })
        .catch();
};

// 换页
const handleChangePage = (newPage: number) => {
    currentPage.value = newPage;
    getNewAnsList(currentPage.value, params.value.answerExplanation, params.value.answerStatus);
};

onMounted(async () => {
    if (fromType === '1') {
        const res: any = await getProjectDetailApi(uniqueCode)
        if (res.success) {
            const data = res.data.list[0]
            title.value = data.title
        }
    } else if (fromType === '2') {
        const res: any = await getExerDetailApi(uniqueCode)
        if (res.success) {
            const data = res.data
            title.value = data.stem
        }
    }
    const res2: any = await getAnswersApi(questionId)
    if (res2.success) {
        const data = res2.data.questionAndAnswer[0]
        curQuestion.userName = data.userName
        curQuestion.questionType = data.questionType
        curQuestion.keyword = data.keyword
        curQuestion.questionNecessity = data.questionNecessity
        curQuestion.questionWeight = data.questionWeight
        curQuestion.createTime = data.createTime
        curQuestion.questionState = data.questionState
        curQuestion.questionId = data.questionId
        curQuestion.questionDescription = data.questionDescription
        curQuestion.canDelete = data.canDelete
        curQuestion.associatedWords = data.associatedWords
        curQuestion.answerNumber = data.answerNumber
    }
    const res3: any = await getAnswerListByQuestionId(Number(questionId), 1, 10,)
    if (res3.success) {
        const data = res3.data
        tableData.value = data.records
        total.value = data.total
    }
})
</script>

<style scoped lang="less">
:deep(.el-button--primary.is-text.is-disabled) {
    color: var(--color-invalid);
}

:deep(.el-table .el-table__cell) {
    height: 65px;
    overflow: hidden;
    width: 100%;
    /* 强制宽度为100% */
}

:deep(.el-table .el-table__cell div) {
    max-height: 65px;
    overflow: hidden;
    width: 100% !important;
    /* 强制宽度为100% */
}

/* 省略号文本样式 */
.ellipsis-text {
    display: block;
    white-space: nowrap;
    /* 不换行 */
    overflow: hidden;
    /* 隐藏溢出内容 */
    text-overflow: ellipsis;
    /* 显示省略号 */
    max-width: 100%;
    /* 最大宽度 */
    width: 100%;
    /* 占满容器 */
    color: black;
    /* 确保文字为黑色 */
    position: relative;
    /* 为渐变遮罩定位 */
    max-height: 65px;
    /* 限制最大高度 */
}

:deep(.ellipsis-text p) {
    white-space: nowrap;
    /* 不换行 */
    overflow: hidden;
    /* 隐藏溢出内容 */
    text-overflow: ellipsis;
    /* 显示省略号 */
    display: inline-block;
    max-width: 100%;
    max-height: 65px;
    /* 限制最大高度 */
}

/* 为公式添加渐变遮罩效果 */
.ellipsis-text::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 15px;
    /* 渐变区域宽度 */
    height: 100%;
    background: linear-gradient(to right, rgba(255, 255, 255, 0), white);
    /* 从透明到白色的渐变 */
    pointer-events: none;
    /* 确保不影响鼠标事件 */
    z-index: 1;
    /* 确保在文本上方但在引号下方 */
}

.main-wrapper {
    width: 1200px;
    margin: 20px auto auto auto;
    font-family: var(--font-family-text);
    background-color: white;
    color: var(--color-black);
    padding-bottom: 20px;

    .top {
        width: 1150px;
        height: 60px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #dcdfe6;

        .title {
            font-size: 24px;
            font-weight: 600;
            line-height: 40px;
        }

        .title2 {
            font-size: 16px;
            font-weight: 400;
            line-height: 40px;
        }

        .back-btn {
            cursor: pointer;
            color: var(--color-primary);
        }
    }

    .middle {
        width: 1150px;
        margin: 0 auto;
        margin-top: 12px;
        border-bottom: 1px solid #dcdfe6;

        .user {
            font-size: 12px;
            color: #333333;

            .username {
                font-weight: 600;
                margin-right: 5px;
            }
        }

        .question {
            margin: 15px 0;
        }

        .question-type {
            font-size: 12px;
            font-weight: 600;

            .necessity {
                display: inline-block;
                width: 60px;
                height: 25px;
                line-height: 25px;
                text-align: center;
                background-color: var(--color-primary);
                color: white;
                padding: 0 5px;
                border-radius: 10px;
                margin-right: 10px;
            }

            .weight {
                display: inline-block;
                width: 60px;
                height: 25px;
                line-height: 25px;
                text-align: center;
                background-color: var(--color-explain);
                padding: 0 5px;
                border-radius: 10px;
            }
        }

        .time {
            margin-top: 10px;
            margin-bottom: 15px;
            font-size: 12px;
        }
    }

    .bottom {
        width: 1150px;
        margin: 0 auto;
        margin-top: 5px;

        .line {
            border-top: 1px solid #dcdfe6;
        }

        .content {
            display: inline-block;
            width: auto;
            min-width: 0;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-height: 65px;
        }

        .content p {
            display: inline-block;
            height: auto;
            margin: 0;
            padding: 0;
            max-height: 65px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .operationBlock {
            display: flex;
            width: 100%;
            padding: 0 20px;
            justify-content: space-between;
            align-items: center;
            flex-direction: row;
            color: var(--color-primary);

            .operationBtn {
                padding: 0 10px;

                &:hover {
                    cursor: pointer;
                    color: var(--color-primary);
                    font-weight: 600;
                    /* text-decoration: underline; */
                }
            }

            .disabledBtn {
                padding: 0 10px;
                color: var(--color-light);

                &:hover {
                    cursor: no-drop;
                }
            }
        }
    }
}
</style>