<script setup lang="ts">
// 答案发布
import {
  computed,
  nextTick,
  onMounted,
  onUpdated,
  reactive,
  ref,
  watch,
} from "vue";
import {
  qType,
  qTypeDict,
  typeDict_noDefault,
  taskCompleteTypeDict,
  taskCompleteType,
  ExerciseTypeDict,
} from "@/utils/constant";
import type {
  questionDetail,
  klgType,
  tagType,
  answerItem,
  taskTypePlus,
} from "@/utils/type";
import { findKeyByValue } from "@/utils/func";
import MyButton from "@/components/MyButton.vue";
import MyTag from "./MyTag.vue";
import { ElFormItem, ElMessage, ElStep, FormRules } from "element-plus";
import InlineEditor from "@/components/editors/VeditorInline.vue";
import type { FormInstance } from "element-plus";
import { getKlgListApi } from "@/apis/path/prjdetail";
import { getTagListApi, type params2tList } from "@/apis/path/tagManage";
import { editAnswerApi } from "@/apis/path/answerList";
import type { params2EditAns } from "@/apis/path/answerList";
import { processAllLatexEquations } from '@/utils/latexUtils'

const emits = defineEmits(["edit", "add", "openCnt", "refresh"]);
const loading = ref(false);
const isDrawerShow = ref(false); // 是否展示抽屉
const drawerTitle = ref(); // 抽屉名
const answerRef = ref(); // 回答问题editor
const ruleFormRef = ref<FormInstance>(); //
const rule2FormRef = ref<FormInstance>(); //
const tagList = ref<tagType[]>([]);
const curAnswerKlg = ref<klgType[]>([]); // 当前回答知识点列表
// const isAddTaskShow = ref<boolean>(true) // 是否显示任务
// const isAddTagShow = ref<boolean>(true) // 是否显示klg
const currentPage = ref(1); // 当前页
const pageSize = ref(10); // 当前页大小
const typeClass = ref(); // 项目类型class
const kList = ref(); // 搜索列表
const answerText = ref("");
// 计算未删除的任务数量
const tasksLength = computed(() => {
  return ruleForm.list.filter((task) => !task.deleted).length;
});
const ruleForm = reactive({
  answer: "",
  list: [],
});
const rules = reactive<FormRules>({
  answer: [{ required: true, message: "请输入答案", trigger: "blur" }],
  list: [],
});
const belong = ref(); // 项目: 2 习题: 3
const curAnswer = ref<answerItem>({
  answerExplanation: "",
  answerId: -1,
  answerStatus: -1,
  createTime: "",
  keyword: "",
  klgNumber: 0,
  title: "",
  type: 0,
  questionType: "",
  taskNumber: 0,
  questionDescription: "",
});
const curAnswerDetail = reactive<questionDetail>({
  stem: "",
  questioner: "",
  answerer: "",
  answerExplanation: "",
  answerKlgs: [],
  createTime: "",
  answerStatusList: [],
});
// 设置curAnswer
const setCurAnswer = (data: any) => {
  curAnswer.value.answerExplanation = data.answerExplanation;
  curAnswer.value.answerId = data.answerId;
  curAnswer.value.answerStatus = data.answerStatus;
  curAnswer.value.createTime = data.createTime;
  curAnswer.value.keyword = data.keyword;
  curAnswer.value.klgNumber = data.klgNumber;
  curAnswer.value.title =
    belong.value === 5 || belong.value === 6 ? data.prjTitle : data.stem;
  curAnswer.value.type =
    belong.value === 5 || belong.value === 6
      ? data.prjType
      : data.belongProjectType;
  curAnswer.value.questionType = data.questionType;
  curAnswer.value.taskNumber = data.taskNumber;
  curAnswer.value.questionDescription = data.questionDescription;
};

// 展示抽屉
const showEditDrawer = (item: any, detail: any) => {
  isDrawerShow.value = true;
  drawerTitle.value = "编辑答案";
  belong.value = item.belongProjectType;
  setCurAnswer(item);
  answerText.value = curAnswer.value.answerExplanation;
  // ruleForm.answer = curAnswer.value.answerExplanation
  typeClass.value = "type_" + curAnswer.value.type;
  Object.assign(curAnswerDetail, detail);
  ruleForm.answer = curAnswerDetail.answerExplanation;
  if (detail.stem) {
    curAnswer.value.title = curAnswerDetail.stem;
  }
  if (detail.answerKlgs) curAnswerKlg.value = detail.answerKlgs;
  ruleForm.list = curAnswerDetail.answerStatusList.map((task) => {
    return {
      ...task,
      taskStyle: "feedback-style" + task.taskStatus,
      oid: task.oid ? task.oid : null,
      deleted: false,
    };
  });
};

// 处理删除tag
const handleDelete = (aimId: string) => {
  const newKlg: klgType[] = curAnswerKlg.value.filter(
    (item) => item.code != aimId
  );
  curAnswerKlg.value = newKlg;
};

// 处理添加问题
const handleAddTask = () => {
  if (
    qTypeDict[curAnswer.value.questionType] === qType.what &&
    tasksLength.value === 1
  ) {
    ElMessage.info("最多只能添加一条");
    return;
  }
  const task = ref<taskTypePlus>({
    klgName: "",
    areaTitle: "",
    areaCode: "",
    taskStatus: 0,
    handlerName: "",
    feedback: null,
    oid: null,
    taskStyle: "",
    deleted: false,
  });
  ruleForm.list.push(task.value);
};
// 处理删除任务
const handleDelTask = (task: any) => {
  // 使用findIndex找到task在数组中的索引
  const index = ruleForm.list.findIndex(
    (t) => t.areaTitle === task.areaTitle && t.klgName === task.klgName
  );
  // 如果找到了任务，就使用splice方法删除它
  if (index !== -1) {
    if (task.oid) {
      task.deleted = true;
    } else {
      ruleForm.list.splice(index, 1);
    }
  }
};
// 处理关闭抽屉
const handleClose = () => {
  window.getSelection()?.empty();
  isDrawerShow.value = false;
  curAnswerKlg.value.splice(0, curAnswerKlg.value.length);
  ruleForm.list.splice(0, ruleForm.list.length);
  ruleForm.answer = "";
};

// 生成klg字符串
const generateKlgString = () => {
  return curAnswerKlg.value.map((item) => item.code).join("@@");
};
// 处理编辑答案
const handleEditAnsSubmit = () => {
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      const toEditTaskList = ref([]);
      toEditTaskList.value = ruleForm.list.map((task) => {
        return {
          oid: task.oid ? task.oid : null,
          klgName: task.klgName,
          areaTitle: task.areaTitle,
          areaCode: task.areaCode,
          deleted: task.deleted ? task.deleted : false,
        };
      });
      const params = ref<params2EditAns>({
        answerId: curAnswer.value.answerId,
        answerKlgs: generateKlgString(),
        answerExplanation: ruleForm.answer,
        taskList: toEditTaskList.value,
      });
      editAnswerApi(params.value).then((res) => {
        if (res.success) {
          emits("refresh", true);
          ElMessage.success("编辑成功！");
          handleClose();
        }
      });
    } else {
    }
  });
};

// 筛选选项
const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true;
    const params = ref<params2tList>({
      limit: pageSize.value,
      current: currentPage.value,
      keyword: query,
      status: 1,
    });
    getTagListApi(params.value).then((res) => {
      // @ts-ignore
      if (res.success) {
        loading.value = false;
        tagList.value = res.data.list;
      }
    });
  } else {
    tagList.value = [];
  }
};

// 筛选klg选项
const remoteKlgMethod = (query: string) => {
  if (query) {
    loading.value = true;
    getKlgListApi(query, qTypeDict[curAnswer.value.questionType]).then(
      (res) => {
        if (res.success) {
          loading.value = false;
          kList.value = res.data;
        }
      }
    );
  } else {
    kList.value = [];
  }
};

// 处理选择klg
const handleSelectKlg = (item: klgType) => {
  if (qType[curAnswer.value.questionType] === qType.what) {
    if (curAnswerKlg.value.length === 1) {
      ElMessage.error("最多选择1个标签");
      return;
    }
  }
  item.choose = !item.choose;
  // 维护klgList
  const index = curAnswerKlg.value.findIndex(
    (i: klgType) => i.code === item.code
  );
  if (index === -1) {
    curAnswerKlg.value.push(item);
  } else {
    curAnswerKlg.value.splice(index, 1);
  }
};

// 处理选择tag
const handleSelectTag = (tag: tagType, item: taskTypePlus) => {
  item.areaCode = tag.areaCode;
  item.areaTitle = tag.title;
};

const taglength = computed(() => curAnswerKlg.value.length)
const tasklength = computed(() => tasksLength.value)

const isAddTagShow = computed(() => {
  if (qTypeDict[curAnswer.value.questionType] == qType.what) {
    const totalLength = taglength.value + tasklength.value
    if (totalLength === 1) {
      return taglength.value > 0
    } else {
      return true
    }
  } else if (qType[curAnswer.value.questionType] === qType.open) {
    return false;
  } else {
    return true;
  }
})

const isAddTaskShow = computed(() => {
  if (qTypeDict[curAnswer.value.questionType] == qType.what) {
    const totalLength = taglength.value + tasklength.value
    if (totalLength === 1) {
      return tasklength.value > 0
    } else {
      return true
    }
  } else if (qType[curAnswer.value.questionType] === qType.open) {
    return false;
  } else {
    return true;
  }
})

defineExpose({
  showEditDrawer,
  isDrawerShow,
});
</script>

<template>
  <el-drawer
    direction="ltr"
    @close="handleClose"
    class="question-drawer"
    size="45%"
    v-model="isDrawerShow"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :z-index="10"
    style="color: var(--color-black)"
  >
    <template #header="{ titleId }">
      <div>
        <div :id="titleId" class="title">
          {{ drawerTitle }}
        </div>
        <div class="line"></div>
      </div>
    </template>
    <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules">
      <div class="content-container">
        <div class="content-header">
          <div class="prj-header">
            <span class="header-prj-title">
              <span
                style="font-size: 16px"
                class="ck-content questionList"
                v-html="curAnswer.title"
              ></span>
            </span>
            <span class="header-prj-type" :class="typeClass">
              {{
                belong === 5 || belong === 6
                  ? (findKeyByValue(
                      curAnswer.type.toString(),
                      typeDict_noDefault
                    ) as string)
                  : (findKeyByValue(curAnswer.type, ExerciseTypeDict) as string)
              }}
            </span>
          </div>
          <div class="question-header">
            <span class="tip">
              <span class="ques-username">
                <b>{{ curAnswerDetail.questioner }}</b>
              </span>
              的提问</span
            >
            <div class="related-content-container">
              <span style="max-width: 85%; display: flex">
                <b class="ques-mark">【</b
                ><b
                  class="ck-content questionList"
                  v-html="(curAnswer.keyword)"
                ></b
                ><b class="ques-mark">】</b>
              </span>
              <span>{{ curAnswer.questionType }}</span>
            </div>
            <div v-if="qTypeDict[curAnswer.questionType] === qType.open">
              <span class="ques-decription">
                <span
                  class="ck-content"
                  v-html="curAnswer.questionDescription"
                ></span>
              </span>
            </div>
            <div class="ques-timebar">
              <span>
                {{ curAnswer.createTime }}
              </span>
            </div>
          </div>
        </div>
        <!-- 是什么：1个知识点 开放问题：0个知识点 其他：无所谓 -->
        <el-form-item>
          <span class="add-answer-user-info">
            <span
              ><b>{{ curAnswerDetail.answerer }} </b></span
            >
            <span style="margin-left: 10px">的回答</span>
          </span>
        </el-form-item>
        <el-form-item prop="answer">
          <inlineEditor
            v-model="ruleForm.answer"
            :disabled="false"
            style="min-width: 100%; min-height: 150px; max-height: 540px"
            :showToolbar="true"
          ></inlineEditor>
        </el-form-item>
        <el-form-item
          v-if="
            qTypeDict[curAnswer.questionType] !== qType.open && isAddTagShow
          "
        >
          <div style="width: 100%" class="klg-block">
            <span>请把答案中的知识点选出来:</span>
            <div style="margin-top: 10px">
              <el-select
                v-if="qTypeDict[curAnswer.questionType] !== qType.what"
                multiple
                filterable
                :fit-input-width="true"
                suffix-icon="Search"
                remote
                reserve-keyword
                placeholder="请输入知识名称"
                placement="top"
                :remote-method="remoteKlgMethod"
                :remote-show-suffix="true"
                :loading="loading"
                collapse-tags
                :max-collapse-tags="0"
              >
                <el-option
                  v-for="item in kList"
                  :key="item.code"
                  :value="item.title"
                  :class="item.choose ? 'highlight' : ''"
                  style="width: 100%"
                  @click="handleSelectKlg(item)"
                >
                  <span class="option-tag" :class="`option-tag${item.type}`">{{
                    item.type === 2 ? "领域" : "知识"
                  }}</span>
                  <span class="ck-content" v-html="item.title"></span>
                </el-option>
              </el-select>
              <el-select
                v-else
                filterable
                suffix-icon="Search"
                :remote-show-suffix="true"
                remote
                reserve-keyword
                placeholder="请输入知识名称"
                placement="top"
                :remote-method="remoteKlgMethod"
                :loading="loading"
              >
                <el-option
                  v-for="item in kList"
                  :key="item.code"
                  :value="item.title"
                  :class="item.choose ? 'highlight' : ''"
                  style="width: 100%"
                  @click="handleSelectKlg(item)"
                >
                  <span class="option-tag" :class="`option-tag${item.type}`">{{
                    item.type === 2 ? "领域" : "知识"
                  }}</span>
                  <span
                    class="ck-content title-text"
                    v-html="item.title"
                  ></span>
                </el-option>
              </el-select>
            </div>
            <div class="selected-list" id="bottom">
              <my-tag
                class="k"
                @delete="handleDelete"
                :tag-id="item.code"
                v-for="item in curAnswerKlg"
                :key="item.code"
              >
                <el-tooltip raw-content popper-class="tooltip-width">
                  <template #content>
                    <div
                      class="tooltipHtml ck-content"
                      v-html="item.title"
                    ></div>
                  </template>
                  <span
                    class="htmlContent2 ck-content"
                    v-html="item.title"
                  ></span>
                </el-tooltip>
              </my-tag>
            </div>
          </div>
        </el-form-item>
        <el-form-item
          v-if="
            isAddTagShow &&
            isAddTaskShow &&
            qTypeDict[curAnswer.questionType] !== qType.open
          "
        >
          <div class="line"></div>
        </el-form-item>
        <el-form-item
          v-if="
            qTypeDict[curAnswer.questionType] !== qType.open && isAddTaskShow
          "
        >
          <div style="width: 100%">
            <div class="add-klg" style="width: 100%">
              <span> 如果没有找到合适的知识点,请点这里 </span>
              <span class="icon" @click="handleAddTask">
                <img src="@/assets/images/answerlist/u736.svg" />
              </span>
            </div>
          </div>
        </el-form-item>
        <el-form-item
          v-if="
            qTypeDict[curAnswer.questionType] !== qType.open && isAddTaskShow
          "
          v-for="(item, index) in ruleForm.list"
          :key="item.areaCode"
        >
          <div class="task-list" v-if="!item.deleted">
            <el-col :span="16">
              <el-form-item
                :prop="`list.${index}.klgName`"
                :rules="{
                  required: true,
                  message: '请输入任务名称',
                  trigger: 'blur',
                }"
                style="width: 100%"
              >
                <el-input
                  v-model="item.klgName"
                  class="img-size"
                  style="width: 100%; margin-right: 10px"
                  placeholder="请输入任务名称"
                  :disabled="item.oid ? true : false"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                :prop="`list.${index}.areaTitle`"
                :rules="{
                  required: true,
                  message: '请选择领域',
                  trigger: 'blur',
                }"
                style="width: 100%"
              >
                <el-select
                  v-model="item.areaTitle"
                  filterable
                  :fit-input-width="true"
                  suffix-icon="Search"
                  clearable
                  remote
                  reserve-keyword
                  placeholder="选择领域"
                  :remote-method="remoteMethod"
                  :loading="loading"
                  :validate-event="true"
                  :disabled="item.oid ? true : false"
                >
                  <el-option
                    v-for="tag in tagList"
                    :key="tag.title"
                    :label="tag.title"
                    :value="tag.title"
                    v-html="tag.title"
                    class="ck-content"
                    style="width: 100%"
                    @click="handleSelectTag(tag, item)"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <span class="icon" @click="handleDelTask(item)">
                <img
                  v-if="item.taskStatus !== taskCompleteType.assigned"
                  style="margin-top: 10px"
                  src="@/assets/images/answerlist/u742.svg"
                />
              </span>
            </el-col>
          </div>
          <div
            v-if="
              item.taskStatus !== null && item.taskStatus !== 1 && !item.deleted
            "
            style="width: 91.7%"
          >
            <div
              class="feedback-content"
              :class="item.taskStyle"
              v-if="item.taskStatus"
            >
              <div class="status">
                <b>{{
                  findKeyByValue(
                    item.taskStatus,
                    taskCompleteTypeDict
                  ) as string
                }}</b>
              </div>
              <div v-if="item.taskStatus === 2">
                <div class="handler">
                  {{ item.handlerName }}
                </div>
              </div>
              <div
                v-else-if="
                  item.taskStatus === 3 ||
                  item.taskStatus === 4 ||
                  item.taskStatus === 6
                "
              >
                <div class="handler">
                  {{ item.handlerName }}
                </div>
                <div class="klg">
                  <span class="ck-content" v-html="(item.klgTitle)"></span>
                </div>
              </div>
              <div v-else-if="item.taskStatus === 5">
                <div class="handler">
                  {{ item.handlerName }}
                </div>
                <div class="klg">
                  {{ item.feedback }}
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item> </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <div class="footer">
        <div class="btn-group">
          <my-button type="light" @click="handleClose">返回</my-button>
          <my-button @click="handleEditAnsSubmit">提交</my-button>
        </div>
      </div>
    </template>
  </el-drawer>
</template>

<style scoped>
/* el-dialog的渲染是通过teleport直接挂到body下边，在这里写样式不生效，所以把样式写到全局(App.vue)了 */
:deep(.el-select__caret.is-reverse) {
  transform: rotate(0deg);
}
:deep(.el-form-item__content) {
  align-items: center;
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  font-size: var(--font-size);
  line-height: 19px;
  min-width: 0;
  position: relative;
}

.highlight {
  background-color: var(--color-light);
  color: var(--color-primary);
}
.question-drawer {
  color: var(--color-black);
}
.icon {
  margin-left: 10px;
  display: flex;

  width: 30px;
  &:hover {
    cursor: pointer;
  }
}

.el-select-dropdown__item {
  color: var(--color-black);
  font-family: var(--font-family-text);

  &:hover * {
    font-weight: 600;
  }

  &.selected {
    color: var(--color-primary);
  }
}

.title {
  font-size: 16px;
  font-family: var(--font-family-text);
}

.content-container {
  display: flex;
  flex-direction: column;
  font-family: var(--text-family);
  padding-top: 0px;
  :deep(.is-disabled .el-textarea__inner),
  :deep(.is-disabled .el-input__wrapper) {
    background-color: white;
    resize: none;
    /*禁止文本域拖拽事件*/
  }
  .content-header {
    .prj-header {
      font-family: var(--title-family);
      overflow: hidden;
      margin-bottom: 10px;
      :deep(p) {
        display: inline;
      }
      .header-prj-title {
        font-size: var(--fontsize-large-header);
        margin-right: 10px;
        font-weight: bold;
      }
      .header-prj-type {
        border: solid 1px;
        border-radius: 3px;
        font-size: var(--fontsize-small-header);
        padding: 0 5px;
        overflow: hidden;
      }
      .type_1 {
        border-color: var(--color-explain);
        color: var(--color-explain);
      }
      .type_2 {
        border-color: var(--color-exam);
        color: var(--color-exam);
      }
      .type_3 {
        border-color: var(--color-case);
        color: var(--color-case);
      }
    }
    .question-header {
      background-color: var(--color-back-header);
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 10px;
    }
  }

  .input {
    /* width: 800px; */
    width: 100%;
    line-height: 20px;
  }
  .el-select {
    width: 100%;
  }
  .not-select-tag {
    background-color: white;
    color: black;
    border: 1px solid var(--color-primary);
  }
  .select-tag {
    &:hover {
      background-color: var(--color-primary);
      color: white;
    }
  }
  .tag {
    border-radius: 5px;
  }
  .ques-decription {
    display: flex;
    text-align: center;
    min-width: 100%;
    border: 1px solid var(--color-primary);
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 10px;
  }
  .ques-username {
    margin-right: 10px;
  }
  .ques-mark {
    display: flex;
    align-items: center;
  }
  .ques-timebar {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--color-grey);
    .answer {
      font-style: normal;
      font-size: 12px;
      color: #1973cb;
      text-align: right;
      &:hover {
        cursor: pointer;
      }
    }
  }
  .answer-item {
    width: 100%;
    padding: 10px 10px;
    background-color: rgba(214, 233, 246, 0.996078431372549);
    margin-bottom: 10px;
    border-radius: 3px;
    .info {
      margin-bottom: 10px;
      font-size: 12px;
      .author {
        margin: 0 5px;
        background-color: rgba(238, 162, 60, 0.1);
        border: 1px solid rgba(238, 162, 60, 0.3);
        color: #e6a23c;
        border-radius: 4px;
        height: 8px;
      }
    }
  }
  .line {
    position: relative;
    width: 100%;
    height: 1px;
    margin-top: 10px;
    background-color: #ccc;
  }
  .klg-list {
    display: flex;
    margin-bottom: 10px;
    .k {
      width: 100px;
      height: 25px;
      padding: 5px;
      font-size: 12px;
      margin-right: 10px;
      border-radius: 3px;
    }
  }
  .answer-exp {
    margin-bottom: 10px;
    font-size: 16px;
  }
  .tooltip {
    display: flex;
    justify-content: space-between;
    color: var(--color-deep);
  }
  .tip {
    margin-bottom: 6px;
  }

  .related-content-container {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: var(--color-back-header);
    word-break: break-all;
  }

  .klg-wrapper {
    width: 100%;
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
  }

  .selected-list {
    display: flex;
    padding-top: 5px;
    flex-wrap: wrap;
    .k {
      border-radius: 5px;
      margin-bottom: 5px;
      margin-right: 10px;
      width: 120px;
    }
  }
  .add-klg {
    width: 100%;
    display: flex;
    align-items: center;
  }
  .task-list {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .select-area {
      margin-left: 10px;
      width: 35%;
    }
    .icon {
      margin-left: 10px;
      display: flex;
      align-items: center;
      &:hover {
        cursor: pointer;
      }
    }
  }
  .feedback-content {
    width: 100%;
    border-radius: 3px;
    padding: 5px 15px;
    .status {
      font-size: 14px;
    }
    .handler {
      font-size: 12px;
    }
    .klg {
      font-size: 14px;
    }
  }
  .feedback-style2 {
    color: var(--color-assigned);
    background-color: var(--color-assigned-rgba);
  }
  .feedback-style3 {
    color: var(--color-completed);
    background-color: var(--color-completed-rgba);
  }
  .feedback-style4 {
    color: var(--color-completed);
    background-color: var(--color-completed-rgba);
  }
  .feedback-style6 {
    color: var(--color-primary);
    background-color: var(--color-primary-rgba);
  }
  .feedback-style5 {
    color: var(--color-returned);
    background-color: var(--color-returned-rgba);
  }
  .add-answer {
    width: 100%;
    .selectedTList {
      width: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      overflow: auto;
      .t {
        margin: 0 10px 10px 0;
        padding: 0 10px 0 10px;
      }
    }

    .answer-tooltip {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .answer-submit {
        padding: 5px;
      }
    }
  }
  .button-list {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
  }
}
.el-drawer {
  :deep() {
    margin-bottom: 0px;
  }
}

.footer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 20px;

  .btn-group {
    display: flex;
    justify-content: space-between;
    width: 280px;
  }

  .pagination {
    display: flex;
    flex-direction: row;

    .btn {
      cursor: pointer;
      margin: 0 5px;
      display: flex;
      width: 10px;
      height: 10px;
      border-radius: 5px;
      background-color: var(--color-grey);

      &.focused,
      &:hover {
        background-color: var(--color-primary);
      }
    }
  }
}
l .option-tag {
  margin-right: 10px;
  border: 1px solid black;
  padding: 0 5px;
  border-radius: 3px;
  font-weight: 400 !important;
}
.option-tag1 {
  color: #606266;
}
.option-tag2 {
  color: var(--color-primary);
  border-color: var(--color-primary);
}
</style>
<style>
.el-drawer__header {
  position: relative;
  padding-bottom: 10px;
}

.el-drawer__header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
p {
  display: inline;
}
</style>
