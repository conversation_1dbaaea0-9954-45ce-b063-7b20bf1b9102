import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { answer, questionItem } from '@/utils/type';

export const useDialogStore = defineStore("dialogStore", () => {
    const answerDialogVisible = ref(false)
    const question = ref<questionItem>()

    const questionDialogVisible = ref(false)

    const editDialogVisible = ref(false)
    const answerId = ref()
    const questionId = ref()

    const refresh = ref(false)

    return { answerDialogVisible, question, questionDialogVisible, editDialogVisible, answerId, questionId, refresh }
})